## DTC Template — Build Log

This log tracks every change to the project in small, auditable steps. Each step lists the intent, actions taken, artifacts changed, and next actions. Dates use UTC.

### Conventions
- **Step format**: Step N — Title (YYYY-MM-DD)
- **Artifacts**: files/directories impacted
- **Commands**: shell commands executed
- **Verification**: how we verified success
- **Next**: suggested immediate next steps

---

### Step 1 — Initialize Next.js 15 application (2025-08-07)

- **Intent**: Bootstrap a modern Next.js 15 app with TypeScript, App Router, Tailwind, ESLint, `src/` directory, and clean import alias `@/*`.
- **Location**: `dtc-template/` inside the workspace root.
- **Commands**:
  - `npx create-next-app@latest dtc-template --ts --tailwind --eslint --src-dir --app --import-alias "@/*"`
- **Artifacts**:
  - Created directory: `dtc-template/`
  - Initialized git repository in `dtc-template/`
  - Added core files: `src/app`, `tailwind.config.ts`, `postcss.config.mjs`, `tsconfig.json`, `package.json`, `.eslintrc.json`, etc.
- **Key versions** (from `package.json`):
  - next: `15.4.6`
  - react: `19.1.0`
  - react-dom: `19.1.0`
  - typescript: `^5`
  - tailwindcss: `^4`
- **Verification**:
  - Confirmed successful scaffold and dependency installation.
  - Confirmed Next.js version in `package.json` as `15.4.6`.
- **Notes**:
  - The workspace directory has spaces/capitals; to satisfy npm naming rules, the app was created under `dtc-template/`.
- **Next**:
  1. Add Prettier + project linting conventions (import/order, Tailwind plugin).
  2. Configure strict TypeScript and path aliases in `tsconfig.json` (ensure `@/*` is wired).
  3. Establish baseline CI (lint, typecheck, build) and run dev to verify.
  4. Prepare i18n/RTL foundation (next-intl, `/(en)` default, `/(ar)` support, `dir` handling).

---

### Step 2 — Verify production build (2025-08-07)

- **Intent**: Ensure the freshly scaffolded app builds successfully in production mode.
- **Commands**:
  - `npm run build`
- **Artifacts**:
  - Generated `.next/` production build output.
- **Verification**:
  - Build completed successfully with Next.js `15.4.6` and React `19.1.0`.
  - Static routes generated without errors; linting and type checks passed.
- **Next**:
  1. Add Prettier and enforce formatting on commit (Husky + lint-staged).
  2. Turn on TypeScript strictness and finalize `paths`/`baseUrl` for `@/*`.
  3. Set up i18n scaffolding (next-intl) with `/(en)` default and `/(ar)` support, `lang`/`dir` wiring.
  4. Initialize Tailwind design tokens and UI guidelines document.

---

### Step 3 — Firebase client SDK, structure, env example, and rules (2025-08-07)

- **Intent**: Set up Firebase client (Auth + Firestore) without Admin SDK, provide clean architecture to avoid duplicate SDK calls, and enable an easy initial user setup flow.
- **Commands**:
  - `npm install firebase`
  - Created folders: `src/app/Firebase/Authentication`, `src/Services`, `src/hooks`, `src/lib`
  - Created `firestore.rules` at project root
  - Added `.env.local.example` with placeholders
- **Artifacts**:
  - `src/app/Firebase/init.ts` — singleton Firebase app/auth/db initialization
  - `src/app/Firebase/Authentication/AuthProvider.tsx` — user context provider
  - `src/app/Firebase/Authentication/SetupGate.tsx` — optional initial setup UI (env‑gated)
  - `src/Services/authService.ts` — auth actions wrapper
  - `src/Services/firestoreService.ts` — Firestore helpers (CRUD + timestamps)
  - `src/hooks/useAuth.ts`, `src/hooks/useFirestoreDoc.ts`, `src/hooks/useFirestoreCollection.ts`
  - `firestore.rules` — default: authenticated users only
  - Updated `src/app/layout.tsx` to wrap app with `AuthProvider` and `SetupGate`
  - `.env.local.example` with Firebase keys and `NEXT_PUBLIC_ENABLE_SETUP`
- **Verification**:
  - Type checks and build expected to pass (no Admin SDK, client‑only)
  - Provider mounts in layout; setup gate only visible when `NEXT_PUBLIC_ENABLE_SETUP=true`
- **Next**:
  1. Add Firebase emulator config and scripts for local development.
  2. Create minimal auth pages (sign in, reset) wired to services (or rely on SetupGate for dev).
  3. Document environment setup and how to disable SetupGate after first user is created.

---

### Step 4 — Typography setup (Montserrat + local Glancyr & Greta Text Arabic) (2025-08-07)

- **Intent**: Configure brand typography: Montserrat (Google) for English, Greta Text Arabic for Arabic, Glancyr for English display headings (optional, local licensed files).
- **Actions**:
  - Added `src/styles/fonts.ts` with `next/font/google` Montserrat (weights 300/400/600/700).
  - Updated `src/app/layout.tsx` to use Montserrat CSS variable.
  - Added `public/fonts/glancyr/` and `public/fonts/greta-text-arabic/` directories with `.gitkeep` placeholders.
  - Declared `@font-face` rules in `src/app/globals.css` for Glancyr and Greta Text Arabic; set CSS variables and `:lang(ar)` font-family.
- **Notes**:
  - Place licensed font files (woff2/woff) into the created folders using the given filenames to enable them.
  - Default English stack uses Montserrat. Arabic stack uses Greta Text Arabic; falls back to Noto Kufi Arabic.
- **Cleanup** (2025-08-07):
  - Copied only required fonts to `public/fonts`:
    - Glancyr: Light/Regular/SemiBold/Bold OTF + license
    - Greta Text Arabic: Regular TTF
  - Updated `globals.css` @font-face to match these files.
  - Removed extraneous font sources under `src/styles/*` and `.DS_Store`.
- **Next**:
  1. i18n scaffolding (`en` default, `ar` supported) + `lang`/`dir` handling.
  2. Typography guidelines doc with usage across locales.


