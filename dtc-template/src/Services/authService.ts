import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signOut,
  updateProfile,
  type User,
} from "firebase/auth";
import { getFirebaseClients } from "@/app/Firebase/init";

export type Credentials = { email: string; password: string };

export async function emailPasswordSignIn({ email, password }: Credentials) {
  const { auth } = getFirebaseClients();
  const result = await signInWithEmailAndPassword(auth, email, password);
  return result.user;
}

export async function emailPasswordSignUp({ email, password }: Credentials, displayName?: string) {
  const { auth } = getFirebaseClients();
  const result = await createUserWithEmailAndPassword(auth, email, password);
  if (displayName) await updateProfile(result.user, { displayName });
  return result.user;
}

export async function requestPasswordReset(email: string) {
  const { auth } = getFirebaseClients();
  await sendPasswordResetEmail(auth, email);
}

export async function logout() {
  const { auth } = getFirebaseClients();
  await signOut(auth);
}

export type PublicUser = Pick<User, "uid" | "email" | "displayName" | "photoURL"> & { emailVerified: boolean };

export function toPublicUser(user: User | null): PublicUser | null {
  if (!user) return null;
  const { uid, email, displayName, photoURL, emailVerified } = user;
  return { uid, email: email ?? null, displayName: displayName ?? null, photoURL: photoURL ?? null, emailVerified } as PublicUser;
}


