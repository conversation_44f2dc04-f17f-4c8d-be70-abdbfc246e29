"use client";

import { AuthLayout, LoginForm } from "@/components/ui/auth";
import type { LoginFormData } from "@/lib/validations/auth";

export default function LoginPage() {
  const handleLogin = async (data: LoginFormData) => {
    // TODO: Implement actual authentication logic
    console.log("Login attempt:", data);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // For now, just log the data
    // In a real app, you would:
    // 1. Call your authentication API
    // 2. Handle success/error responses
    // 3. Redirect on successful login
    // 4. Store auth tokens/session
  };

  return (
    <AuthLayout heroImage="hero1">
      <LoginForm onSubmit={handleLogin} />
    </AuthLayout>
  );
}
