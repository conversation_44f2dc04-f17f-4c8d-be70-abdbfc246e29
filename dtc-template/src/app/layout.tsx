import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { montserrat } from "@/styles/fonts";

export const metadata: Metadata = {
  title: "DTC Portal",
  description: "Digital Transformation Center Portal",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html>
      <body className={`${montserrat.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
