import type { Metadata } from "next";
import "./globals.css";
import AuthProvider from "@/app/Firebase/Authentication/AuthProvider";
import SetupGate from "@/app/Firebase/Authentication/SetupGate";
import { montserrat } from "@/styles/fonts";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { getDirection, type Locale } from "@/i18n/config";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();
  const dir = getDirection(locale as Locale);
  return (
    <html lang={locale} dir={dir}>
      <body className={`${montserrat.variable} antialiased`}>
        <NextIntlClientProvider messages={messages} locale={locale} timeZone={Intl.DateTimeFormat().resolvedOptions().timeZone}>
          <AuthProvider>
            <SetupGate>{children}</SetupGate>
          </AuthProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
